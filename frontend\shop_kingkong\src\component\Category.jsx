import React from "react";

const Category = () => {
  const bags = [
    {
      name: "Túi Casual",
      image: "https://via.placeholder.com/300x400",
      size: "large",
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      image: "https://via.placeholder.com/300x300",
      size: "medium",
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      image: "https://via.placeholder.com/300x350",
      size: "medium",
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      image: "https://via.placeholder.com/300x300",
      size: "medium",
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      image: "https://via.placeholder.com/300x350",
      size: "medium",
    },
  ];

  return (
    <div className="bg-gray-50 min-h-screen p-6">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-black mb-4"><PERSON><PERSON></h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          <PERSON><PERSON><PERSON><PERSON> <PERSON>h<PERSON> những chiế<PERSON> túi xa hoa, ch<PERSON><PERSON><PERSON> nghi<PERSON> và phong cách độc đáo
          để làm nổi bật cá tính riêng của bạn
        </p>
      </div>

      {/* Masonry Grid Layout */}
      <div className="max-w-6xl mx-auto">
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
          {/* Túi Casual - Large */}
          <div className="col-span-1 row-span-2">
            <div className="relative group cursor-pointer">
              <img
                src={bags[0].image}
                alt={bags[0].name}
                className="w-full h-full object-cover rounded-lg shadow-md hover:shadow-lg transition-shadow"
              />
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4 rounded-b-lg">
                <p className="text-white font-medium text-lg">{bags[0].name}</p>
              </div>
            </div>
          </div>

          {/* Túi Văn Phòng */}
          <div className="col-span-1">
            <div className="relative group cursor-pointer">
              <img
                src={bags[1].image}
                alt={bags[1].name}
                className="w-full h-48 object-cover rounded-lg shadow-md hover:shadow-lg transition-shadow"
              />
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3 rounded-b-lg">
                <p className="text-white font-medium">{bags[1].name}</p>
              </div>
            </div>
          </div>

          {/* Túi Đa Hơi */}
          <div className="col-span-1">
            <div className="relative group cursor-pointer">
              <img
                src={bags[2].image}
                alt={bags[2].name}
                className="w-full h-56 object-cover rounded-lg shadow-md hover:shadow-lg transition-shadow"
              />
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3 rounded-b-lg">
                <p className="text-white font-medium">{bags[2].name}</p>
              </div>
            </div>
          </div>

          {/* Túi Sang Trọng */}
          <div className="col-span-1">
            <div className="relative group cursor-pointer">
              <img
                src={bags[3].image}
                alt={bags[3].name}
                className="w-full h-48 object-cover rounded-lg shadow-md hover:shadow-lg transition-shadow"
              />
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3 rounded-b-lg">
                <p className="text-white font-medium">{bags[3].name}</p>
              </div>
            </div>
          </div>

          {/* Túi Thiết Kế */}
          <div className="col-span-1">
            <div className="relative group cursor-pointer">
              <img
                src={bags[4].image}
                alt={bags[4].name}
                className="w-full h-56 object-cover rounded-lg shadow-md hover:shadow-lg transition-shadow"
              />
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3 rounded-b-lg">
                <p className="text-white font-medium">{bags[4].name}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Category;
